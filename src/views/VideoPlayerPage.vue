<template>
  <div class="video-player-page">
    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 左侧视频播放区域 -->
      <div class="video-section">
        <!-- 视频播放器 -->
        <div class="video-player-container">
          <video
              ref="videoPlayer"
              :src="currentVideo?.videoUrl"
              controls
              @loadedmetadata="onVideoLoaded"
              @timeupdate="onTimeUpdate"
              @ended="onVideoEnded"
          />
        </div>

        <!-- 视频信息 -->
        <div class="video-info">
          <h2>{{ currentVideo?.videoTitle }}</h2>
          <div class="video-meta">
            <a-space>
              <span class="video-duration">
                <ClockCircleOutlined/>
                {{ formatDuration(currentVideo?.videoDuration || 0) }}
              </span>
              <span class="upload-time">
                <CalendarOutlined/>
                {{ formatDate(currentVideo?.createTime as string) }}
              </span>
            </a-space>
          </div>
        </div>

        <!-- AI视频总结区域 -->
        <div class="ai-summary-section">
          <a-card title="AI视频总结" size="small">
            <div v-if="videoSummaries.length > 0" class="summary-content">
              <div class="custom-timeline">
                <div
                    v-for="summary in videoSummaries"
                    :key="summary.ai_video_summary_id"
                    class="timeline-item-custom"
                    :class="{ 'active-item': isCurrentTimeInRange(summary) }"
                >
                  <div class="timeline-dot">
                    <a-button
                        type="link"
                        size="small"
                        @click="seekToTime(summary.start_seconds)"
                        :class="{ 'active-summary': isCurrentTimeInRange(summary) }"
                        class="time-button"
                    >
                      {{ formatDuration(summary.start_seconds) }}
                    </a-button>
                  </div>
                  <div class="timeline-content">
                    <div class="summary-text">
                      {{ summary.summary_content }}
                    </div>
                    <div class="summary-duration">
                      {{ formatDuration(summary.start_seconds) }} - {{ formatDuration(summary.end_seconds) }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <a-empty v-else description="暂无AI总结"/>
          </a-card>
        </div>
      </div>

      <!-- 右侧信息面板 -->
      <div class="info-panel">
        <!-- 视频详细信息 -->
        <a-card title="视频信息" size="small" style="margin-bottom: 16px;">
          <div class="video-info-detail">
            <div class="info-item">
              <span class="label">视频时长：</span>
              <span class="value">{{ formatDuration(currentVideo?.videoDuration || 0) }}</span>
            </div>
            <div class="info-item">
              <span class="label">上传时间：</span>
              <span class="value">{{ formatDate(currentVideo?.createTime as string) }}</span>
            </div>
            <div class="info-item">
              <span class="label">视频状态：</span>
              <span class="value">
                <a-tag :color="getStatusColor(currentVideo?.videoStatus)">
                  {{ getStatusText(currentVideo?.videoStatus) }}
                </a-tag>
              </span>
            </div>
          </div>
        </a-card>

        <!-- AI总结 -->
        <a-card title="AI视频总结" size="small" style="margin-bottom: 16px;" v-if="currentVideo?.summary100">
          <div class="ai-summary">
            <p>{{ currentVideo.summary100 }}</p>
          </div>
        </a-card>

        <!-- 学习笔记 -->
        <a-card title="学习笔记" size="small" style="margin-bottom: 16px;">
          <div class="learning-notes">
            <a-empty description="暂无学习笔记" :image="false"/>
            <div style="text-align: center; margin-top: 12px;">
              <a-button type="dashed" size="small">添加笔记</a-button>
            </div>
          </div>
        </a-card>

        <!-- 快速操作 -->
        <a-card title="快速操作" size="small">
          <div class="quick-actions">
            <a-space direction="vertical" style="width: 100%;">
              <a-button type="primary" block @click="goBack">
                <ArrowLeftOutlined/>
                返回视频列表
              </a-button>
              <a-button block>
                <ClockCircleOutlined/>
                添加到收藏
              </a-button>
              <a-button block>
                分享视频
              </a-button>
            </a-space>
          </div>
        </a-card>
      </div>
    </div>


  </div>
</template>

<script setup lang="ts">
import {ref, onMounted, watch} from 'vue'
import {useRoute, useRouter} from 'vue-router'
import {message} from 'ant-design-vue'
import {
  ClockCircleOutlined,
  CalendarOutlined,
  ArrowLeftOutlined
} from '@ant-design/icons-vue'
import {getVideoById} from '@/api/videoController.ts'

// 数据类型定义
interface Video {
  videoId?: number
  uploaderId?: number
  videoTitle?: string
  videoUrl?: string
  originalVideoName?: string
  videoCover?: string
  videoDuration?: number
  videoStatus?: number
  isDelete?: number
  createTime?: string
  updateTime?: string
  textData?: any[]
  summaryData?: any[]
  summary100?: string
}

interface VideoSummary {
  ai_video_summary_id: number
  video_id: number
  audio_id?: number
  start_seconds: number
  end_seconds: number
  summary_content: string
}


// 路由和响应式数据
const route = useRoute()
const router = useRouter()
const videoPlayer = ref<HTMLVideoElement>()
const currentVideo = ref<Video | null>(null)
const relatedVideos = ref<Video[]>([])
const videoSummaries = ref<VideoSummary[]>([])
const currentTime = ref(0)

// 默认封面图片 (使用base64编码的简单图片)
const defaultCoverImage = ref('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIwIiBoZWlnaHQ9IjY4IiB2aWV3Qm94PSIwIDAgMTIwIDY4IiBmaWxsPSJub25lIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPgo8cmVjdCB3aWR0aD0iMTIwIiBoZWlnaHQ9IjY4IiBmaWxsPSIjRjVGNUY1Ii8+CjxwYXRoIGQ9Ik00OCA0MEw2NCAzMkw0OCAyNFY0MFoiIGZpbGw9IiM4Qzg5OEMiLz4KPHN2Zz4K')

// 初始化
onMounted(() => {
  const videoId = route.params.videoId as string
  if (videoId) {
    loadVideoData(parseInt(videoId))
  }
})

// 监听路由变化
watch(() => route.params.videoId, (newVideoId) => {
  if (newVideoId) {
    loadVideoData(parseInt(newVideoId as string))
  }
})

// 数据加载方法
const loadVideoData = async (videoId: number) => {
  try {
    // 加载当前视频信息
    await loadCurrentVideo(videoId)
    // 加载相关视频列表
    await loadRelatedVideos()
    // 加载AI总结
    await loadVideoSummaries(videoId)
  } catch (error) {
    message.error('加载视频数据失败')
  }
}

const loadCurrentVideo = async (videoId: number) => {
  try {
    const response = await getVideoById({id: videoId})
    if (response.data.code === 0) {
      currentVideo.value = response.data.data as Video
    } else {
      message.error('获取视频信息失败')
    }
  } catch (error) {
    console.error('加载视频失败:', error)
    message.error('加载视频失败')
  }
}

const loadRelatedVideos = async () => {
  // 暂时使用空数组，后续可以根据需要加载相关视频
  relatedVideos.value = []
}

const loadVideoSummaries = async (videoId: number) => {
  // 模拟加载AI总结
  const mockSummaries: VideoSummary[] = [
    {
      ai_video_summary_id: 1,
      video_id: videoId,
      start_seconds: 0,
      end_seconds: 300,
      summary_content: '介绍Java变量的基本概念，包括什么是变量、为什么需要变量以及变量在程序中的重要作用。'
    },
    {
      ai_video_summary_id: 2,
      video_id: videoId,
      start_seconds: 300,
      end_seconds: 600,
      summary_content: '详细讲解Java的基本数据类型：int、double、boolean、char等，以及它们的特点和使用场景。'
    },
    {
      ai_video_summary_id: 3,
      video_id: videoId,
      start_seconds: 600,
      end_seconds: 900,
      summary_content: '演示变量的声明和初始化语法，包括不同的声明方式和最佳实践。'
    },
    {
      ai_video_summary_id: 4,
      video_id: videoId,
      start_seconds: 900,
      end_seconds: 1200,
      summary_content: '讲解变量的作用域概念，包括局部变量、实例变量和类变量的区别。'
    },
    {
      ai_video_summary_id: 5,
      video_id: videoId,
      start_seconds: 1200,
      end_seconds: 1500,
      summary_content: '介绍变量命名规范和Java编程中的命名约定，帮助写出更规范的代码。'
    },
    {
      ai_video_summary_id: 6,
      video_id: videoId,
      start_seconds: 1500,
      end_seconds: 1800,
      summary_content: '总结本节课的重点内容，并提供一些练习建议和进阶学习方向。'
    }
  ]

  videoSummaries.value = mockSummaries
}

// 视频播放控制方法
const playVideo = (video: Video) => {
  // 更新路由参数，触发视频切换
  router.push(`/video/${video.videoId}`)
}

const seekToTime = (seconds: number) => {
  if (videoPlayer.value) {
    videoPlayer.value.currentTime = seconds
    videoPlayer.value.play()
  }
}

const onVideoLoaded = () => {
  // 视频加载完成后的处理
  console.log('视频加载完成')
}

const onTimeUpdate = () => {
  if (videoPlayer.value) {
    currentTime.value = videoPlayer.value.currentTime
  }
}

const onVideoEnded = () => {
  // 视频播放结束，自动播放下一个视频
  const currentIndex = relatedVideos.value.findIndex(v => v.videoId === currentVideo.value?.videoId)
  if (currentIndex >= 0 && currentIndex < relatedVideos.value.length - 1) {
    const nextVideo = relatedVideos.value[currentIndex + 1]
    playVideo(nextVideo)
  }
}

const goBack = () => {
  router.go(-1)
}

// 图片加载错误处理
const handleImageError = (event: Event) => {
  const img = event.target as HTMLImageElement
  img.src = defaultCoverImage.value
}

// 计算属性和工具方法
const isCurrentTimeInRange = (summary: VideoSummary) => {
  return currentTime.value >= summary.start_seconds && currentTime.value <= summary.end_seconds
}

const formatDuration = (seconds: number) => {
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const secs = Math.floor(seconds % 60)

  if (hours > 0) {
    return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
  }
  return `${minutes}:${secs.toString().padStart(2, '0')}`
}

const formatDate = (dateStr: string) => {
  return new Date(dateStr).toLocaleString('zh-CN')
}

// 获取状态标签颜色
const getStatusColor = (status: number | undefined) => {
  if (!status) return 'default'
  const colors = {
    0: 'processing', // 上传中
    1: 'warning',    // 处理中
    2: 'success',    // 完成
    3: 'error'       // 失败
  }
  return colors[status as keyof typeof colors] || 'default'
}

// 获取状态文本
const getStatusText = (status: number | undefined) => {
  if (!status) return '未知'
  const texts = {
    0: '上传中',
    1: '处理中',
    2: '完成',
    3: '失败'
  }
  return texts[status as keyof typeof texts] || '未知'
}


</script>

<style scoped>
.video-player-page {
  padding: 24px;
  background-color: #f5f5f5;
  min-height: 100vh;
  position: relative;
}

.main-content {
  display: flex;
  gap: 24px;
  max-width: 1400px;
  margin: 0 auto;
}

/* 左侧视频区域 */
.video-section {
  flex: 2;
  min-width: 0;
}

.video-player-container {
  background: #000;
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 16px;
}

.video-player-container video {
  width: 100%;
  height: auto;
  min-height: 500px;
  max-height: 700px;
}

.video-info {
  background: white;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 16px;
}

.video-info h2 {
  margin: 0 0 12px 0;
  color: #1890ff;
  font-size: 24px;
}

.video-meta {
  color: #666;
}

.video-meta .anticon {
  margin-right: 4px;
}

/* AI总结区域 */
.ai-summary-section {
  background: white;
  border-radius: 8px;
}

.summary-content {
  max-height: 400px;
  overflow-y: auto;
  padding-right: 8px;
}

.summary-text {
  margin-bottom: 8px;
  line-height: 1.6;
  word-wrap: break-word;
  word-break: break-word;
  white-space: pre-wrap;
  color: #333;
  font-size: 14px;
}

.summary-duration {
  font-size: 12px;
  color: #999;
  margin-top: 4px;
}

.active-summary {
  color: #1890ff !important;
  font-weight: bold;
}

/* 自定义时间轴样式 */
.custom-timeline {
  padding: 0;
}

.timeline-item-custom {
  display: flex;
  margin-bottom: 20px;
  position: relative;
}

.timeline-item-custom:not(:last-child)::after {
  content: '';
  position: absolute;
  left: 40px;
  top: 32px;
  bottom: -20px;
  width: 2px;
  background-color: #f0f0f0;
}

.timeline-item-custom.active-item::after {
  background-color: #1890ff;
}

.timeline-dot {
  flex-shrink: 0;
  width: 80px;
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
  position: relative;
}

.timeline-dot::before {
  content: '';
  position: absolute;
  left: 38px;
  top: 12px;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #d9d9d9;
  border: 2px solid #fff;
  box-shadow: 0 0 0 1px #d9d9d9;
}

.timeline-item-custom.active-item .timeline-dot::before {
  background-color: #1890ff;
  box-shadow: 0 0 0 1px #1890ff;
}

.time-button {
  padding: 0 !important;
  height: auto !important;
  line-height: 1.2 !important;
  font-size: 12px !important;
  min-width: 60px;
  text-align: left;
}

.timeline-content {
  flex: 1;
  padding-left: 16px;
  min-width: 0;
}

/* 右侧信息面板区域 */
.info-panel {
  flex: 1;
  min-width: 350px;
}

/* 视频详细信息样式 */
.video-info-detail {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.info-item:last-child {
  border-bottom: none;
}

.info-item .label {
  font-weight: 500;
  color: #666;
}

.info-item .value {
  color: #333;
}

/* AI总结样式 */
.ai-summary {
  line-height: 1.6;
  color: #555;
  background: #f8f9fa;
  padding: 12px;
  border-radius: 6px;
  border-left: 3px solid #1890ff;
}

/* 学习笔记样式 */
.learning-notes {
  text-align: center;
}

/* 快速操作样式 */
.quick-actions {
  padding: 8px 0;
}

.playlist-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #f0f0f0;
}

.playlist-header h4 {
  margin: 0;
  color: #1890ff;
}

.video-count {
  color: #666;
  font-size: 12px;
}

.video-list {
  max-height: 600px;
  overflow-y: auto;
}

.video-item {
  display: flex;
  gap: 12px;
  padding: 12px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s;
  margin-bottom: 8px;
}

.video-item:hover {
  background-color: #f5f5f5;
}

.video-item.active {
  background-color: #e6f7ff;
  border: 1px solid #1890ff;
}

.video-thumbnail {
  position: relative;
  width: 120px;
  height: 68px;
  border-radius: 4px;
  overflow: hidden;
  flex-shrink: 0;
}

.video-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.video-index {
  position: absolute;
  top: 4px;
  left: 4px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
}

.video-duration-overlay {
  position: absolute;
  bottom: 4px;
  right: 4px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
}

.playing-indicator {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: #1890ff;
  font-size: 24px;
}

.video-details {
  flex: 1;
  min-width: 0;
}

.video-title {
  margin: 0 0 8px 0;
  font-size: 14px;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.video-meta-small {
  display: flex;
  gap: 12px;
  font-size: 12px;
  color: #999;
}

/* 返回按钮 */
.back-button {
  position: fixed;
  top: 100px;
  left: 24px;
  z-index: 100;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .main-content {
    flex-direction: column;
  }

  .info-panel {
    min-width: auto;
  }

  .video-list {
    max-height: 300px;
  }
}

@media (max-width: 768px) {
  .video-player-page {
    padding: 16px;
  }

  .main-content {
    gap: 16px;
  }

  .video-thumbnail {
    width: 100px;
    height: 56px;
  }

  .back-button {
    top: 80px;
    left: 16px;
  }
}
</style>
